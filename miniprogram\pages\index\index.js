// 《能量星球》星际舰桥 - 主界面逻辑
Page({
  data: {
    // 页面状态
    loading: false
  },

  onLoad: function (options) {
    console.log('星际舰桥加载');
    this.initializePage();
  },

  onReady: function () {
    console.log('星际舰桥渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新能量数据
    this.refreshEnergyData();
  },

  onHide: function () {
    // 页面隐藏时保存数据
    this.saveUserData();
  },

  onUnload: function () {
    console.log('星际舰桥卸载');
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshEnergyData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  onShareAppMessage: function () {
    return {
      title: '来《能量星球》开始你的宇宙探索之旅！',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-cover.jpg'
    }
  },

  // 初始化页面
  initializePage() {
    // 加载用户数据
    this.loadUserData();

    // 播放背景音乐
    this.playBackgroundMusic();
  },

  // 加载用户数据
  loadUserData() {
    // TODO: 从本地存储或服务器加载用户数据
    const userData = wx.getStorageSync('userData') || {};

    this.setData({
      captainName: userData.captainName || '小探索者',
      wisdomEnergy: userData.wisdomEnergy || 150,
      loveEnergy: userData.loveEnergy || 89
    });
  },

  // 保存用户数据
  saveUserData() {
    const userData = {
      captainName: this.data.captainName,
      wisdomEnergy: this.data.wisdomEnergy,
      loveEnergy: this.data.loveEnergy,
      lastSaveTime: Date.now()
    };

    wx.setStorageSync('userData', userData);
  },

  // 刷新能量数据
  refreshEnergyData() {
    // TODO: 实现能量数据刷新逻辑
    console.log('刷新能量数据');
  },

  // 播放背景音乐
  playBackgroundMusic() {
    // TODO: 实现背景音乐播放
    console.log('播放星际舰桥背景音乐');
  },

  // 显示开发中提示
  showDevelopmentToast(moduleName) {
    wx.showModal({
      title: '🚀 ' + moduleName,
      content: '功能正在开发中，敬请期待！\n\n我们正在为您打造最棒的宇宙探索体验。',
      showCancel: false,
      confirmText: '期待中',
      confirmColor: '#4D9FFF'
    });
  },

  // 导航到思维工坊
  onNavigateToWorkshop() {
    console.log('点击思维工坊');
    this.showDevelopmentToast('思维工坊');

    // TODO: 未来实现
    // wx.navigateTo({
    //   url: '/pages/workshop/index'
    // });
  },

  // 导航到船长舱室
  onNavigateToQuarters() {
    console.log('点击船长舱室');
    this.showDevelopmentToast('船长舱室');

    // TODO: 未来实现
    // wx.navigateTo({
    //   url: '/pages/quarters/index'
    // });
  },

  // 导航到宇宙灯塔
  onNavigateToBeacon() {
    console.log('点击宇宙灯塔');
    this.showDevelopmentToast('宇宙灯塔计划');

    // TODO: 未来实现
    // wx.navigateTo({
    //   url: '/pages/beacon/index'
    // });
  },

  // 导航到家长中心
  onNavigateToParent() {
    console.log('点击家长中心');

    // 保存当前活跃时间
    wx.setStorageSync('lastActiveTime', Date.now());

    // 导航到地球指挥部
    wx.navigateTo({
      url: '/pages/parent/index'
    });
  },

  // 开始今日任务
  onStartDailyMission() {
    console.log('点击今日任务');
    this.showDevelopmentToast('舰长日志系统');

    // TODO: 未来实现今日任务逻辑
  },

  // 打开设置
  onOpenSettings() {
    console.log('点击设置');
    this.showDevelopmentToast('飞船系统设置');
  },

  // 打开通讯
  onOpenCommunication() {
    console.log('点击通讯');
    this.showDevelopmentToast('星际通讯系统');
  }
});
