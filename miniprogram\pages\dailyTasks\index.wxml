<!--《能量星球》小探险家的每日冒险-->
<view class="page daily-adventure">
  <!-- 可爱的标题区域 -->
  <view class="adventure-header">
    <!-- 标题装饰 -->
    <view class="title-decoration">
      <text class="deco-star">⭐</text>
      <text class="deco-rainbow">🌈</text>
      <text class="deco-star">⭐</text>
    </view>

    <!-- 主标题 -->
    <view class="adventure-title">
      <text class="title-text">小探险家的每日冒险</text>
      <text class="subtitle-text">今天又是充满惊喜的一天！</text>
    </view>

    <!-- 今日进度 -->
    <view class="daily-progress">
      <view class="progress-container">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{dailyProgress}}%"></view>
          <view class="progress-stars">
            <text class="star-icon" wx:for="{{totalTasks}}" wx:key="*this"
                  style="left: {{(index + 0.5) / totalTasks * 100}}%">
              {{index < completedTasks ? '⭐' : '☆'}}
            </text>
          </view>
        </view>
        <view class="progress-text">
          <text class="progress-message">{{progressMessage}}</text>
        </view>
      </view>

      <!-- 今日能量收获 -->
      <view class="energy-collection">
        <view class="energy-item love">
          <text class="energy-icon">❤️</text>
          <text class="energy-count">{{todayLoveEnergy}}</text>
        </view>
        <view class="energy-item wisdom">
          <text class="energy-icon">💎</text>
          <text class="energy-count">{{todayWisdomEnergy}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 可爱的背景装饰 -->
  <view class="cute-background">
    <!-- 飘浮的小装饰 -->
    <view class="floating-decorations">
      <text class="float-item heart">💖</text>
      <text class="float-item star">✨</text>
      <text class="float-item rainbow">🌈</text>
      <text class="float-item cloud">☁️</text>
      <text class="float-item flower">🌸</text>
      <text class="float-item butterfly">🦋</text>
    </view>
  </view>

  <!-- 冒险任务区域 -->
  <view class="adventure-tasks">
    <!-- 生活小管家 -->
    <view class="adventure-card life-card" bindtap="onOpenLifeTasks">
      <view class="card-character">
        <text class="character-emoji">🏠</text>
        <text class="character-face">😊</text>
      </view>
      <view class="card-content">
        <text class="card-title">生活小管家</text>
        <text class="card-subtitle">让生活变得井井有条</text>
        <view class="task-preview">
          <view class="mini-task" wx:for="{{lifeTasksPreview}}" wx:key="id">
            <text class="task-status-icon">{{item.status === 'completed' ? '✨' : item.status === 'in_progress' ? '⏰' : '💤'}}</text>
            <text class="mini-task-name">{{item.name}}</text>
          </view>
        </view>
        <view class="card-progress">
          <text class="progress-emoji">{{lifeTasksStats.completed === lifeTasksStats.total ? '🎉' : '💪'}}</text>
          <text class="progress-text">{{lifeTasksStats.completed}}/{{lifeTasksStats.total}} 完成</text>
        </view>
      </view>
    </view>

    <!-- 爱心小天使 -->
    <view class="adventure-card family-card" bindtap="onOpenFamilyTasks">
      <view class="card-character">
        <text class="character-emoji">❤️</text>
        <text class="character-face">🥰</text>
      </view>
      <view class="card-content">
        <text class="card-title">爱心小天使</text>
        <text class="card-subtitle">用爱心温暖家庭</text>
        <view class="task-preview">
          <view class="mini-task" wx:for="{{familyTasksPreview}}" wx:key="id">
            <text class="task-status-icon">{{item.status === 'completed' ? '✨' : item.status === 'in_progress' ? '⏰' : '💤'}}</text>
            <text class="mini-task-name">{{item.name}}</text>
          </view>
        </view>
        <view class="card-progress">
          <text class="progress-emoji">{{familyTasksStats.completed === familyTasksStats.total ? '🎉' : '💪'}}</text>
          <text class="progress-text">{{familyTasksStats.completed}}/{{familyTasksStats.total}} 完成</text>
        </view>
      </view>
    </view>

    <!-- 友谊小使者 -->
    <view class="adventure-card social-card" bindtap="onOpenSocialTasks">
      <view class="card-character">
        <text class="character-emoji">🤝</text>
        <text class="character-face">😄</text>
      </view>
      <view class="card-content">
        <text class="card-title">友谊小使者</text>
        <text class="card-subtitle">结交更多好朋友</text>
        <view class="task-preview">
          <view class="mini-task" wx:for="{{socialTasksPreview}}" wx:key="id">
            <text class="task-status-icon">{{item.status === 'completed' ? '✨' : item.status === 'in_progress' ? '⏰' : '💤'}}</text>
            <text class="mini-task-name">{{item.name}}</text>
          </view>
        </view>
        <view class="card-progress">
          <text class="progress-emoji">{{socialTasksStats.completed === socialTasksStats.total ? '🎉' : '💪'}}</text>
          <text class="progress-text">{{socialTasksStats.completed}}/{{socialTasksStats.total}} 完成</text>
        </view>
      </view>
    </view>

    <!-- 地球小卫士 -->
    <view class="adventure-card world-card" bindtap="onOpenSocialParticipationTasks">
      <view class="card-character">
        <text class="character-emoji">🌍</text>
        <text class="character-face">🌟</text>
      </view>
      <view class="card-content">
        <text class="card-title">地球小卫士</text>
        <text class="card-subtitle">保护我们的美丽地球</text>
        <view class="task-preview">
          <view class="mini-task" wx:for="{{participationTasksPreview}}" wx:key="id">
            <text class="task-status-icon">{{item.status === 'completed' ? '✨' : item.status === 'in_progress' ? '⏰' : '💤'}}</text>
            <text class="mini-task-name">{{item.name}}</text>
          </view>
        </view>
        <view class="card-progress">
          <text class="progress-emoji">{{participationTasksStats.completed === participationTasksStats.total ? '🎉' : '💪'}}</text>
          <text class="progress-text">{{participationTasksStats.completed}}/{{participationTasksStats.total}} 完成</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 可爱的底部按钮 -->
  <view class="cute-bottom-controls">
    <view class="cute-button back-button {{backAnimating ? 'bouncing' : ''}}" bindtap="onGoBack">
      <view class="button-decoration">
        <text class="deco-item">🌟</text>
        <text class="deco-item">✨</text>
        <text class="deco-item">🌟</text>
      </view>
      <view class="button-content">
        <text class="button-icon">🏠</text>
        <text class="button-text">回到星球</text>
      </view>
    </view>

    <view class="cute-button achievement-button {{achievementsAnimating ? 'bouncing' : ''}}" bindtap="onOpenAchievements">
      <view class="button-decoration">
        <text class="deco-item">🏆</text>
        <text class="deco-item">🎉</text>
        <text class="deco-item">🏆</text>
      </view>
      <view class="button-content">
        <text class="button-icon">🏅</text>
        <text class="button-text">我的奖章</text>
      </view>
    </view>
  </view>
</view>
