<!--《能量星球》今日任务中心-->
<view class="page task-center">
  <!-- HUD 任务中心抬头显示器 -->
  <view class="task-hud">
    <!-- HUD扫描线系统 -->
    <view class="hud-scanner">
      <view class="scan-line horizontal"></view>
      <view class="scan-line vertical"></view>
      <view class="data-stream stream-left"></view>
      <view class="data-stream stream-right"></view>
    </view>

    <!-- 任务中心标题 -->
    <view class="task-title">
      <view class="title-glow"></view>
      <view class="hologram-corners">
        <view class="corner corner-tl"></view>
        <view class="corner corner-tr"></view>
        <view class="corner corner-bl"></view>
        <view class="corner corner-br"></view>
      </view>
      <text class="title-text">今日任务中心</text>
      <text class="subtitle-text">Daily Mission Center</text>
    </view>

    <!-- 今日进度概览 -->
    <view class="daily-overview">
      <view class="progress-summary">
        <text class="progress-text">今日完成：{{completedTasks}}/{{totalTasks}}</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{dailyProgress}}%"></view>
        </view>
      </view>
      <view class="energy-summary">
        <view class="energy-item">
          <text class="energy-icon">❤️</text>
          <text class="energy-count">{{todayLoveEnergy}}</text>
        </view>
        <view class="energy-item">
          <text class="energy-icon">💎</text>
          <text class="energy-count">{{todayWisdomEnergy}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 宇宙背景 -->
  <view class="space-background">
    <!-- 星空层 -->
    <view class="stars-layer">
      <view class="star star-1"></view>
      <view class="star star-2"></view>
      <view class="star star-3"></view>
      <view class="star star-4"></view>
    </view>

    <!-- 悬浮粒子网络 -->
    <view class="particle-network">
      <view class="energy-particle love-particle particle-1"></view>
      <view class="energy-particle wisdom-particle particle-2"></view>
      <view class="energy-particle love-particle particle-3"></view>
      <view class="energy-particle wisdom-particle particle-4"></view>
      <view class="energy-particle love-particle particle-5"></view>
      <view class="energy-particle wisdom-particle particle-6"></view>

      <!-- 粒子连接线 -->
      <svg class="particle-connections" width="100%" height="100%">
        <defs>
          <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#FF6B9D;stop-opacity:0" />
            <stop offset="50%" style="stop-color:#4D9FFF;stop-opacity:0.6" />
            <stop offset="100%" style="stop-color:#63E2B7;stop-opacity:0" />
          </linearGradient>
        </defs>
        <line class="connection-line line-1" x1="20%" y1="30%" x2="80%" y2="70%" stroke="url(#connectionGradient)" stroke-width="2"/>
        <line class="connection-line line-2" x1="30%" y1="60%" x2="70%" y2="40%" stroke="url(#connectionGradient)" stroke-width="2"/>
        <line class="connection-line line-3" x1="15%" y1="80%" x2="85%" y2="20%" stroke="url(#connectionGradient)" stroke-width="2"/>
      </svg>
    </view>

    <!-- 数据矩阵雨 -->
    <view class="matrix-rain">
      <view class="matrix-column column-1">
        <text class="matrix-char">0</text>
        <text class="matrix-char">1</text>
        <text class="matrix-char">0</text>
      </view>
      <view class="matrix-column column-2">
        <text class="matrix-char">1</text>
        <text class="matrix-char">0</text>
        <text class="matrix-char">1</text>
      </view>
      <view class="matrix-column column-3">
        <text class="matrix-char">0</text>
        <text class="matrix-char">1</text>
        <text class="matrix-char">0</text>
      </view>
    </view>
  </view>

  <!-- 主任务控制台区域 -->
  <view class="main-console">
    <!-- 生活习惯星球 -->
    <view class="task-module life-tasks" bindtap="onOpenTaskCategory" data-category="life">
      <view class="module-glow life-glow"></view>

      <!-- 全息边框系统 -->
      <view class="hologram-frame">
        <view class="holo-corner holo-tl"></view>
        <view class="holo-corner holo-tr"></view>
        <view class="holo-corner holo-bl"></view>
        <view class="holo-corner holo-br"></view>
        <view class="holo-edge holo-top"></view>
        <view class="holo-edge holo-right"></view>
        <view class="holo-edge holo-bottom"></view>
        <view class="holo-edge holo-left"></view>
      </view>

      <!-- 能量波纹效果 -->
      <view class="energy-ripple"></view>

      <view class="module-header">
        <text class="module-icon">🏠</text>
        <text class="module-title">生活习惯</text>
        <view class="status-indicator life-indicator"></view>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value neon-number">{{lifeTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value neon-number">{{lifeTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="neon-progress-bar" style="width: {{lifeTasksProgress}}%">
            <view class="progress-glow"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 家庭关爱星球 -->
    <view class="task-module family-tasks" bindtap="onOpenTaskCategory" data-category="family">
      <view class="module-glow family-glow"></view>
      <view class="hologram-frame">
        <view class="holo-corner holo-tl"></view>
        <view class="holo-corner holo-tr"></view>
        <view class="holo-corner holo-bl"></view>
        <view class="holo-corner holo-br"></view>
      </view>
      <view class="energy-ripple"></view>

      <view class="module-header">
        <text class="module-icon">❤️</text>
        <text class="module-title">家庭关爱</text>
        <view class="status-indicator family-indicator"></view>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value neon-number">{{familyTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value neon-number">{{familyTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="neon-progress-bar" style="width: {{familyTasksProgress}}%">
            <view class="progress-glow"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 社交友谊星球 -->
    <view class="task-module social-tasks" bindtap="onOpenTaskCategory" data-category="social">
      <view class="module-glow social-glow"></view>
      <view class="hologram-frame"><view class="holo-corner holo-tl"></view><view class="holo-corner holo-tr"></view><view class="holo-corner holo-bl"></view><view class="holo-corner holo-br"></view></view>
      <view class="energy-ripple"></view>
      <view class="module-header">
        <text class="module-icon">🤝</text>
        <text class="module-title">社交友谊</text>
        <view class="status-indicator social-indicator"></view>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value neon-number">{{socialTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value neon-number">{{socialTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="neon-progress-bar" style="width: {{socialTasksProgress}}%"><view class="progress-glow"></view></view>
        </view>
      </view>
    </view>

    <!-- 社会参与星球 -->
    <view class="task-module participation-tasks" bindtap="onOpenTaskCategory" data-category="participation">
      <view class="module-glow participation-glow"></view>
      <view class="hologram-frame"><view class="holo-corner holo-tl"></view><view class="holo-corner holo-tr"></view><view class="holo-corner holo-bl"></view><view class="holo-corner holo-br"></view></view>
      <view class="energy-ripple"></view>
      <view class="module-header">
        <text class="module-icon">🌍</text>
        <text class="module-title">社会参与</text>
        <view class="status-indicator participation-indicator"></view>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value neon-number">{{participationTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value neon-number">{{participationTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="neon-progress-bar" style="width: {{participationTasksProgress}}%"><view class="progress-glow"></view></view>
        </view>
      </view>
    </view>

    <!-- 今日成就 -->
    <view class="task-module achievements" bindtap="onOpenAchievements">
      <view class="module-glow achievement-glow"></view>
      <view class="hologram-frame"><view class="holo-corner holo-tl"></view><view class="holo-corner holo-tr"></view><view class="holo-corner holo-bl"></view><view class="holo-corner holo-br"></view></view>
      <view class="energy-ripple"></view>
      <view class="module-header">
        <text class="module-icon">🏅</text>
        <text class="module-title">今日成就</text>
        <view class="status-indicator achievement-indicator"></view>
      </view>
      <view class="module-content">
        <view class="achievement-preview">
          <view class="achievement-item" wx:for="{{todayAchievements}}" wx:key="id">
            <text class="achievement-badge neon-badge">{{item.badge}}</text>
            <text class="achievement-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 习惯追踪 -->
    <view class="task-module habits" bindtap="onOpenHabits">
      <view class="module-glow habit-glow"></view>
      <view class="hologram-frame"><view class="holo-corner holo-tl"></view><view class="holo-corner holo-tr"></view><view class="holo-corner holo-bl"></view><view class="holo-corner holo-br"></view></view>
      <view class="energy-ripple"></view>
      <view class="module-header">
        <text class="module-icon">📈</text>
        <text class="module-title">习惯追踪</text>
        <view class="status-indicator habit-indicator"></view>
      </view>
      <view class="module-content">
        <view class="habit-streak">
          <text class="streak-number neon-number">{{longestStreak}}</text>
          <text class="streak-label">天连续</text>
        </view>
        <view class="habit-chart">
          <view class="chart-bar neon-bar" wx:for="{{habitChart}}" wx:key="*this"
                style="height: {{item}}%; animation-delay: {{index * 0.1}}s"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动魔法传送门 -->
  <view class="magic-portal {{portalExpanded ? 'expanded' : ''}}" bindtap="onTogglePortal">
    <view class="portal-core">
      <text class="portal-icon">✨</text>
      <view class="portal-rings">
        <view class="ring" wx:for="{{3}}" wx:key="*this"></view>
      </view>
    </view>

    <!-- 扇形菜单 -->
    <view class="portal-menu" wx:if="{{portalExpanded}}">
      <view class="menu-item home" bindtap="onGoBack" data-action="home">
        <text class="menu-icon">🚀</text>
        <text class="menu-text">返回舰桥</text>
      </view>

      <view class="menu-item achievements" bindtap="onOpenAchievements" data-action="achievements">
        <text class="menu-icon">🏅</text>
        <text class="menu-text">成就馆</text>
      </view>

      <view class="menu-item settings" bindtap="onOpenSettings" data-action="settings">
        <text class="menu-icon">⚙️</text>
        <text class="menu-text">设置</text>
      </view>
    </view>
  </view>

  <!-- 任务详情弹窗 -->
  <view class="task-detail-modal {{selectedCategory ? 'show' : ''}}" bindtap="onCloseModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">{{selectedCategoryInfo.title}}</text>
        <text class="modal-subtitle">{{selectedCategoryInfo.subtitle}}</text>
      </view>

      <view class="modal-tasks">
        <view class="task-item {{item.status}}" wx:for="{{selectedCategoryTasks}}" wx:key="id"
              bindtap="onToggleTask" data-task-id="{{item.id}}">
          <view class="task-status">
            <text class="status-icon">{{item.status === 'completed' ? '✅' : item.status === 'in_progress' ? '⏰' : '⭕'}}</text>
          </view>
          <view class="task-info">
            <text class="task-name">{{item.name}}</text>
            <text class="task-desc" wx:if="{{item.description}}">{{item.description}}</text>
          </view>
          <view class="task-reward">
            <text class="reward-icon">{{item.energyReward.type === 'love' ? '❤️' : '💎'}}</text>
            <text class="reward-amount">+{{item.energyReward.amount}}</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="close-button" bindtap="onCloseModal">
          <text>继续任务</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 庆祝动画层 -->
  <view class="celebration-layer {{celebrating ? 'active' : ''}}">
    <view class="confetti-container">
      <view class="confetti" wx:for="{{10}}" wx:key="*this"></view>
    </view>
    <view class="celebration-text">
      <text class="celebration-message">{{celebrationMessage}}</text>
    </view>
  </view>
</view>
