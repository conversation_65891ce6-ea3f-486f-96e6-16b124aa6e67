<!--《能量星球》今日任务中心-->
<view class="page task-center">
  <!-- HUD 任务中心抬头显示器 -->
  <view class="task-hud">
    <!-- 任务中心标题 -->
    <view class="task-title">
      <view class="title-glow"></view>
      <text class="title-text">今日任务中心</text>
      <text class="subtitle-text">Daily Mission Center</text>
    </view>

    <!-- 今日进度概览 -->
    <view class="daily-overview">
      <view class="progress-summary">
        <text class="progress-text">今日完成：{{completedTasks}}/{{totalTasks}}</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{dailyProgress}}%"></view>
        </view>
      </view>
      <view class="energy-summary">
        <view class="energy-item">
          <text class="energy-icon">❤️</text>
          <text class="energy-count">{{todayLoveEnergy}}</text>
        </view>
        <view class="energy-item">
          <text class="energy-icon">💎</text>
          <text class="energy-count">{{todayWisdomEnergy}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 宇宙背景 -->
  <view class="space-background">
    <!-- 星空层 -->
    <view class="stars-layer">
      <view class="star star-1"></view>
      <view class="star star-2"></view>
      <view class="star star-3"></view>
      <view class="star star-4"></view>
    </view>
    <!-- 数据流动层 -->
    <view class="data-streams">
      <view class="data-stream stream-1"></view>
      <view class="data-stream stream-2"></view>
      <view class="data-stream stream-3"></view>
    </view>
  </view>

  <!-- 主任务控制台区域 -->
  <view class="main-console">
    <!-- 生活习惯星球 -->
    <view class="task-module life-tasks" bindtap="onOpenTaskCategory" data-category="life">
      <view class="module-glow life-glow"></view>
      <view class="module-header">
        <text class="module-icon">🏠</text>
        <text class="module-title">生活习惯</text>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value">{{lifeTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value">{{lifeTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="progress-bar" style="width: {{lifeTasksProgress}}%"></view>
        </view>
      </view>
    </view>

    <!-- 家庭关爱星球 -->
    <view class="task-module family-tasks" bindtap="onOpenTaskCategory" data-category="family">
      <view class="module-glow family-glow"></view>
      <view class="module-header">
        <text class="module-icon">❤️</text>
        <text class="module-title">家庭关爱</text>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value">{{familyTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value">{{familyTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="progress-bar" style="width: {{familyTasksProgress}}%"></view>
        </view>
      </view>
    </view>

    <!-- 社交友谊星球 -->
    <view class="task-module social-tasks" bindtap="onOpenTaskCategory" data-category="social">
      <view class="module-glow social-glow"></view>
      <view class="module-header">
        <text class="module-icon">🤝</text>
        <text class="module-title">社交友谊</text>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value">{{socialTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value">{{socialTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="progress-bar" style="width: {{socialTasksProgress}}%"></view>
        </view>
      </view>
    </view>

    <!-- 社会参与星球 -->
    <view class="task-module participation-tasks" bindtap="onOpenTaskCategory" data-category="participation">
      <view class="module-glow participation-glow"></view>
      <view class="module-header">
        <text class="module-icon">🌍</text>
        <text class="module-title">社会参与</text>
      </view>
      <view class="module-content">
        <view class="task-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value">{{participationTasksStats.completed}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">总任务</text>
            <text class="stat-value">{{participationTasksStats.total}}</text>
          </view>
        </view>
        <view class="task-progress">
          <view class="progress-bar" style="width: {{participationTasksProgress}}%"></view>
        </view>
      </view>
    </view>

    <!-- 今日成就 -->
    <view class="task-module achievements" bindtap="onOpenAchievements">
      <view class="module-glow achievement-glow"></view>
      <view class="module-header">
        <text class="module-icon">🏅</text>
        <text class="module-title">今日成就</text>
      </view>
      <view class="module-content">
        <view class="achievement-preview">
          <view class="achievement-item" wx:for="{{todayAchievements}}" wx:key="id">
            <text class="achievement-badge">{{item.badge}}</text>
            <text class="achievement-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 习惯追踪 -->
    <view class="task-module habits" bindtap="onOpenHabits">
      <view class="module-glow habit-glow"></view>
      <view class="module-header">
        <text class="module-icon">📈</text>
        <text class="module-title">习惯追踪</text>
      </view>
      <view class="module-content">
        <view class="habit-streak">
          <text class="streak-number">{{longestStreak}}</text>
          <text class="streak-label">天连续</text>
        </view>
        <view class="habit-chart">
          <view class="chart-bar" wx:for="{{habitChart}}" wx:key="*this"
                style="height: {{item}}%; animation-delay: {{index * 0.1}}s"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部控制栏 -->
  <view class="bottom-controls">
    <view class="control-button back" bindtap="onGoBack">
      <view class="button-content">
        <text class="control-icon">🚀</text>
        <text class="control-text">返回舰桥</text>
      </view>
    </view>

    <view class="control-button refresh" bindtap="onRefreshData">
      <view class="button-content">
        <text class="control-icon">🔄</text>
        <text class="control-text">刷新数据</text>
      </view>
    </view>
  </view>

  <!-- 任务详情弹窗 -->
  <view class="task-detail-modal {{selectedCategory ? 'show' : ''}}" bindtap="onCloseModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">{{selectedCategoryInfo.title}}</text>
        <text class="modal-subtitle">{{selectedCategoryInfo.subtitle}}</text>
      </view>

      <view class="modal-tasks">
        <view class="task-item {{item.status}}" wx:for="{{selectedCategoryTasks}}" wx:key="id"
              bindtap="onToggleTask" data-task-id="{{item.id}}">
          <view class="task-status">
            <text class="status-icon">{{item.status === 'completed' ? '✅' : item.status === 'in_progress' ? '⏰' : '⭕'}}</text>
          </view>
          <view class="task-info">
            <text class="task-name">{{item.name}}</text>
            <text class="task-desc" wx:if="{{item.description}}">{{item.description}}</text>
          </view>
          <view class="task-reward">
            <text class="reward-icon">{{item.energyReward.type === 'love' ? '❤️' : '💎'}}</text>
            <text class="reward-amount">+{{item.energyReward.amount}}</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="close-button" bindtap="onCloseModal">
          <text>继续任务</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 庆祝动画层 -->
  <view class="celebration-layer {{celebrating ? 'active' : ''}}">
    <view class="confetti-container">
      <view class="confetti" wx:for="{{10}}" wx:key="*this"></view>
    </view>
    <view class="celebration-text">
      <text class="celebration-message">{{celebrationMessage}}</text>
    </view>
  </view>
</view>
  <!-- 星球详情弹窗 -->
  <view class="planet-detail-modal {{selectedPlanet ? 'show' : ''}}" bindtap="onCloseModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">{{selectedPlanetInfo.title}}</text>
        <text class="modal-subtitle">{{selectedPlanetInfo.subtitle}}</text>
      </view>

      <view class="modal-tasks">
        <view class="task-item" wx:for="{{selectedPlanetTasks}}" wx:key="id">
          <view class="task-icon">
            <text>{{item.status === 'completed' ? '✨' : item.status === 'in_progress' ? '⏰' : '💤'}}</text>
          </view>
          <view class="task-info">
            <text class="task-name">{{item.name}}</text>
            <text class="task-desc">{{item.description}}</text>
          </view>
          <view class="task-reward">
            <text class="reward-icon">{{item.energyReward.type === 'love' ? '❤️' : '💎'}}</text>
            <text class="reward-amount">+{{item.energyReward.amount}}</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="close-button" bindtap="onCloseModal">
          <text>继续探险</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动魔法传送门 -->
  <view class="magic-portal" bindtap="onTogglePortal">
    <view class="portal-core">
      <text class="portal-icon">✨</text>
    </view>
  </view>

  <!-- 庆祝动画层 -->
  <view class="celebration-layer {{celebrating ? 'active' : ''}}">
    <view class="confetti-container">
      <view class="confetti confetti-{{index}}" wx:for="{{20}}" wx:key="*this"></view>
    </view>
    <view class="celebration-text">
      <text class="celebration-message">{{celebrationMessage}}</text>
    </view>
  </view>
</view>
