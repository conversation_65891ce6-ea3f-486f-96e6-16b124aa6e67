/* 《能量星球》今日任务中心 - 基于地球指挥部设计 */

/* 页面基础样式 */
.page.task-center {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #1A183E 0%, #0D0B1E 70%, #000000 100%);
  color: #FFFFFF;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* HUD 任务中心抬头显示器 */
.task-hud {
  position: relative;
  z-index: 100;
  padding: 20rpx 32rpx;
  background: linear-gradient(180deg, rgba(26, 24, 62, 0.9) 0%, rgba(26, 24, 62, 0) 100%);
  backdrop-filter: blur(15px);
  border-bottom: 1rpx solid rgba(77, 159, 255, 0.3);
}

/* 任务中心标题 */
.task-title {
  position: relative;
  text-align: center;
  padding: 24rpx 0;
  margin: 0 auto;
}

.title-glow {
  position: absolute;
  width: 100%;
  height: 60rpx;
  background: linear-gradient(90deg,
    rgba(77, 159, 255, 0) 0%,
    rgba(77, 159, 255, 0.3) 20%,
    rgba(255, 215, 106, 0.4) 50%,
    rgba(77, 159, 255, 0.3) 80%,
    rgba(77, 159, 255, 0) 100%);
  border-radius: 30rpx;
  filter: blur(8rpx);
  animation: title-pulse 4s infinite ease-in-out;
}

.title-text {
  position: relative;
  z-index: 10;
  font-size: 32rpx;
  font-weight: 700;
  color: #FFD76A;
  text-shadow: 0 2rpx 12rpx rgba(255, 215, 106, 0.6);
  letter-spacing: 2rpx;
  display: block;
}

.subtitle-text {
  position: relative;
  z-index: 10;
  font-size: 20rpx;
  color: #FFFFFF;
  opacity: 0.8;
  letter-spacing: 1rpx;
  margin-top: 4rpx;
  display: block;
}

/* 今日进度概览 */
.daily-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(10px);
}

.progress-summary {
  flex: 1;
}

.progress-text {
  font-size: 22rpx;
  color: #FFFFFF;
  margin-bottom: 8rpx;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #63E2B7 0%, #4D9FFF 100%);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.energy-summary {
  display: flex;
  gap: 16rpx;
  margin-left: 20rpx;
}

.energy-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.energy-icon {
  font-size: 24rpx;
}

.energy-count {
  font-size: 20rpx;
  font-weight: 600;
  color: #FFD76A;
}

/* 宇宙背景 */
.space-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 星空层 */
.stars-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.star {
  position: absolute;
  background: #FFFFFF;
  border-radius: 50%;
  animation: twinkle 3s infinite;
}

.star-1 {
  width: 4rpx;
  height: 4rpx;
  top: 25%;
  left: 20%;
  animation-delay: 0s;
}

.star-2 {
  width: 3rpx;
  height: 3rpx;
  top: 45%;
  right: 25%;
  animation-delay: 1.5s;
}

.star-3 {
  width: 5rpx;
  height: 5rpx;
  top: 70%;
  left: 30%;
  animation-delay: 3s;
}

.star-4 {
  width: 4rpx;
  height: 4rpx;
  bottom: 30%;
  right: 20%;
  animation-delay: 4.5s;
}

/* 数据流动层 */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
}

.data-stream {
  position: absolute;
  width: 2rpx;
  height: 100rpx;
  background: linear-gradient(180deg,
    rgba(77, 159, 255, 0) 0%,
    rgba(77, 159, 255, 0.8) 50%,
    rgba(77, 159, 255, 0) 100%);
  animation: data-flow 3s infinite linear;
}

.stream-1 {
  left: 15%;
  animation-delay: 0s;
}

.stream-2 {
  left: 50%;
  animation-delay: 1s;
}

.stream-3 {
  right: 20%;
  animation-delay: 2s;
}

/* 主任务控制台区域 */
.main-console {
  flex: 1;
  position: relative;
  z-index: 50;
  padding: 40rpx 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 20rpx;
}

/* 任务模块 */
.task-module {
  position: relative;
  background: rgba(26, 24, 62, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.task-module:active {
  transform: scale(0.98);
}

/* 模块发光效果 */
.module-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border-radius: 22rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.life-glow {
  background: linear-gradient(45deg,
    rgba(255, 149, 0, 0.3) 0%,
    rgba(255, 215, 106, 0.3) 100%);
}

.family-glow {
  background: linear-gradient(45deg,
    rgba(255, 107, 157, 0.3) 0%,
    rgba(255, 107, 107, 0.3) 100%);
}

.social-glow {
  background: linear-gradient(45deg,
    rgba(99, 226, 183, 0.3) 0%,
    rgba(77, 159, 255, 0.3) 100%);
}

.participation-glow {
  background: linear-gradient(45deg,
    rgba(77, 159, 255, 0.3) 0%,
    rgba(99, 226, 183, 0.3) 100%);
}

.achievement-glow {
  background: linear-gradient(45deg,
    rgba(255, 215, 106, 0.3) 0%,
    rgba(255, 159, 67, 0.3) 100%);
}

.habit-glow {
  background: linear-gradient(45deg,
    rgba(164, 176, 190, 0.3) 0%,
    rgba(77, 159, 255, 0.3) 100%);
}

.task-module:hover .module-glow {
  opacity: 1;
}

/* 模块头部 */
.module-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.module-icon {
  font-size: 32rpx;
}

.module-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #FFFFFF;
}

/* 模块内容 */
.module-content {
  color: #FFFFFF;
}

/* 任务统计样式 */
.task-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #63E2B7;
  display: block;
  margin-top: 4rpx;
}

.task-progress {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.task-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #63E2B7 0%, #4D9FFF 100%);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

/* 成就预览样式 */
.achievement-preview {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.achievement-badge {
  font-size: 20rpx;
}

.achievement-name {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 习惯追踪样式 */
.habit-streak {
  text-align: center;
  margin-bottom: 16rpx;
}

.streak-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #FFD76A;
  display: block;
}

.streak-label {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
}

.habit-chart {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 40rpx;
}

.chart-bar {
  width: 8rpx;
  background: linear-gradient(to top, #4D9FFF 0%, #63E2B7 100%);
  border-radius: 4rpx;
  animation: chart-grow 0.5s ease-out;
}

/* 底部控制栏 */
.bottom-controls {
  position: relative;
  z-index: 100;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 32rpx;
  background: linear-gradient(0deg, rgba(26, 24, 62, 0.9) 0%, rgba(26, 24, 62, 0) 100%);
  backdrop-filter: blur(15px);
  border-top: 1rpx solid rgba(77, 159, 255, 0.3);
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  background: rgba(77, 159, 255, 0.2);
  border-radius: 12rpx;
  border: 1rpx solid rgba(77, 159, 255, 0.3);
  transition: all 0.3s ease;
  min-width: 160rpx;
}

.control-button:active {
  transform: scale(0.95);
  background: rgba(77, 159, 255, 0.3);
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.control-icon {
  font-size: 24rpx;
}

.control-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}

/* 任务详情弹窗 */
.task-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.task-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: rgba(26, 24, 62, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  padding: 30rpx;
  margin: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
  width: 90%;
  max-width: 600rpx;
}

.modal-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #FFD76A;
  display: block;
  margin-bottom: 8rpx;
}

.modal-subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

/* 任务项样式 */
.modal-tasks {
  margin-bottom: 30rpx;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  margin-bottom: 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.task-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.15);
}

.task-item.completed {
  opacity: 0.7;
}

.task-status {
  margin-right: 12rpx;
}

.status-icon {
  font-size: 24rpx;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.task-desc {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

.task-reward {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.reward-icon {
  font-size: 18rpx;
}

.reward-amount {
  font-size: 18rpx;
  color: #FFD76A;
  font-weight: 600;
}

.modal-footer {
  text-align: center;
}

.close-button {
  padding: 16rpx 32rpx;
  background: rgba(77, 159, 255, 0.3);
  border-radius: 12rpx;
  border: 1rpx solid rgba(77, 159, 255, 0.5);
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.close-button:active {
  transform: scale(0.95);
  background: rgba(77, 159, 255, 0.4);
}

/* 庆祝动画层 */
.celebration-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.celebration-layer.active {
  opacity: 1;
}

.confetti-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.confetti {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background: linear-gradient(45deg, #FF6B9D, #4D9FFF, #63E2B7, #FFD76A);
  animation: confetti-fall 3s ease-out infinite;
}

.confetti:nth-child(odd) {
  background: linear-gradient(45deg, #FFD76A, #FF6B9D);
  animation-duration: 2.5s;
}

.confetti:nth-child(3n) {
  background: linear-gradient(45deg, #63E2B7, #4D9FFF);
  animation-duration: 3.5s;
}

.celebration-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 30rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20rpx);
  animation: celebration-appear 0.5s ease-out;
}

.celebration-message {
  font-size: 32rpx;
  font-weight: bold;
  background: linear-gradient(45deg, #FF6B9D, #4D9FFF, #63E2B7);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbow-text 2s ease-in-out infinite;
}

/* 动画关键帧 */
@keyframes title-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes data-flow {
  0% { transform: translateY(-100rpx); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes chart-grow {
  0% { height: 0; }
  100% { height: var(--height, 20rpx); }
}

@keyframes confetti-fall {
  0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
  100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

@keyframes celebration-appear {
  0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes rainbow-text {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 中心探险家角色区域 */
.explorer-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  width: 300rpx;
  height: 300rpx;
}

.explorer-character {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.explorer-character:active {
  transform: scale(0.95);
}

.explorer-character.happy {
  animation: characterHappy 2s ease-in-out infinite;
}

.explorer-character.excited {
  animation: characterExcited 1s ease-in-out infinite;
}

.character-body {
  position: relative;
  z-index: 5;
  font-size: 80rpx;
  text-align: center;
}

.character-face {
  display: block;
  font-size: 80rpx;
  filter: drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.5));
}

.character-accessories {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.accessory {
  position: absolute;
  font-size: 40rpx;
  animation: accessoryFloat 3s ease-in-out infinite;
}

.accessory.hat {
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
}

.accessory.backpack {
  top: 20rpx;
  right: -20rpx;
  animation-delay: 1s;
}

.accessory.tool {
  bottom: 10rpx;
  left: -20rpx;
  animation-delay: 2s;
}

/* 能量光环系统 */
.energy-aura {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.aura {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.aura-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 2rpx solid;
  animation: auraRing 3s ease-in-out infinite;
}

.love-aura .aura-ring {
  border-color: rgba(255, 107, 157, calc(var(--intensity) * 0.6));
}

.wisdom-aura .aura-ring {
  border-color: rgba(77, 159, 255, calc(var(--intensity) * 0.6));
}

.aura-ring:nth-child(1) {
  width: 120%;
  height: 120%;
  animation-delay: 0s;
}

.aura-ring:nth-child(2) {
  width: 140%;
  height: 140%;
  animation-delay: 1s;
}

.aura-ring:nth-child(3) {
  width: 160%;
  height: 160%;
  animation-delay: 2s;
}

/* 浮动粒子 */
.floating-hearts, .floating-gems {
  position: absolute;
  width: 100%;
  height: 100%;
}

.heart-particle, .gem-particle {
  position: absolute;
  font-size: 20rpx;
  animation: particleFloat 4s ease-in-out infinite;
  opacity: 0.8;
}

.heart-particle:nth-child(1) { top: 20%; left: 30%; animation-delay: 0s; }
.heart-particle:nth-child(2) { top: 40%; right: 25%; animation-delay: 0.5s; }
.heart-particle:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 1s; }

.gem-particle:nth-child(1) { top: 25%; right: 30%; animation-delay: 0.2s; }
.gem-particle:nth-child(2) { bottom: 35%; right: 20%; animation-delay: 0.7s; }
.gem-particle:nth-child(3) { top: 45%; left: 25%; animation-delay: 1.2s; }

/* 等级指示器 */
.level-indicator {
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.level-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.level-stars {
  font-size: 16rpx;
  margin-top: 4rpx;
}

/* 轨道式星球系统 */
.planetary-system {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
  z-index: 15;
  transition: transform 0.5s ease;
}

/* 轨道路径 */
.orbit-path {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx dashed rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: orbitRotate 60s linear infinite;
}

.orbit-1 {
  width: 600rpx;
  height: 600rpx;
}

.orbit-2 {
  width: 800rpx;
  height: 800rpx;
  animation-duration: 80s;
}

/* 星球基础样式 */
.planet {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120rpx;
  height: 120rpx;
  transform-origin: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 16;
}

.planet:active {
  transform: scale(1.1);
}

.planet.active {
  transform: scale(1.2);
  z-index: 8;
}

.planet-surface {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.planet-icon {
  font-size: 48rpx;
  z-index: 2;
  filter: drop-shadow(0 0 10rpx rgba(255, 255, 255, 0.5));
}

/* 星球大气层效果 */
.planet-atmosphere {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  overflow: hidden;
}

.smoke-particle, .heart-particle, .rainbow-particle, .nature-particle {
  position: absolute;
  font-size: 16rpx;
  animation: atmosphereFloat 4s ease-in-out infinite;
  opacity: 0.7;
}

.smoke-particle:nth-child(1) { top: 20%; left: 30%; animation-delay: 0s; }
.smoke-particle:nth-child(2) { top: 60%; right: 25%; animation-delay: 1s; }
.smoke-particle:nth-child(3) { bottom: 30%; left: 40%; animation-delay: 2s; }

/* 星球发光效果 */
.planet-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.planet.active .planet-glow {
  opacity: 1;
  animation: planetGlow 2s ease-in-out infinite;
}

.life-glow {
  background: radial-gradient(circle, rgba(255, 149, 0, 0.4), transparent);
  box-shadow: 0 0 40rpx rgba(255, 149, 0, 0.6);
}

.love-glow {
  background: radial-gradient(circle, rgba(255, 107, 157, 0.4), transparent);
  box-shadow: 0 0 40rpx rgba(255, 107, 157, 0.6);
}

.friendship-glow {
  background: radial-gradient(circle, rgba(99, 226, 183, 0.4), transparent);
  box-shadow: 0 0 40rpx rgba(99, 226, 183, 0.6);
}

.earth-glow {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.4), transparent);
  box-shadow: 0 0 40rpx rgba(76, 175, 80, 0.6);
}

/* 星球背景色 */
.life-planet .planet-surface {
  background: linear-gradient(135deg, #FFB74D, #FF9800);
}

.love-planet .planet-surface {
  background: linear-gradient(135deg, #F48FB1, #E91E63);
}

.friendship-planet .planet-surface {
  background: linear-gradient(135deg, #81C784, #4CAF50);
}

.earth-planet .planet-surface {
  background: linear-gradient(135deg, #64B5F6, #2196F3);
}

/* 任务计数徽章 */
.task-count-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 4rpx 8rpx;
  font-size: 18rpx;
  font-weight: bold;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 3;
}

/* 能量传输光束 */
.energy-beam {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4rpx;
  height: 200rpx;
  transform: translate(-50%, -100%);
  transform-origin: bottom;
  background: linear-gradient(to top, transparent, #ffffff, transparent);
  opacity: 0;
  animation: beamPulse 1s ease-in-out infinite;
}

.planet.active .energy-beam {
  opacity: 1;
}

.beam-particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: beamParticle 2s linear infinite;
}

.beam-particle:nth-child(1) { left: -1rpx; animation-delay: 0s; }
.beam-particle:nth-child(2) { left: -1rpx; animation-delay: 0.4s; }
.beam-particle:nth-child(3) { left: -1rpx; animation-delay: 0.8s; }
.beam-particle:nth-child(4) { left: -1rpx; animation-delay: 1.2s; }
.beam-particle:nth-child(5) { left: -1rpx; animation-delay: 1.6s; }

/* 星球详情弹窗 */
.planet-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.planet-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 30rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.planet-detail-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.modal-subtitle {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.modal-tasks {
  margin-bottom: 30rpx;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.task-icon {
  font-size: 24rpx;
  width: 40rpx;
  text-align: center;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.task-desc {
  font-size: 20rpx;
  color: #666;
  line-height: 1.4;
}

.task-reward {
  display: flex;
  align-items: center;
  gap: 5rpx;
  background: rgba(255, 215, 0, 0.2);
  padding: 8rpx 12rpx;
  border-radius: 15rpx;
}

.reward-icon {
  font-size: 20rpx;
}

.reward-amount {
  font-size: 18rpx;
  font-weight: bold;
  color: #333;
}

.modal-footer {
  text-align: center;
}

.close-button {
  background: linear-gradient(135deg, #4D9FFF, #63E2B7);
  color: white;
  padding: 15rpx 40rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(77, 159, 255, 0.3);
  transition: all 0.3s ease;
}

.close-button:active {
  transform: scale(0.95);
}

/* 浮动魔法传送门 */
.magic-portal {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 30;
  width: 120rpx;
  height: 120rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.magic-portal.expanded {
  transform: scale(1.1);
}

.portal-core {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF6B9D, #4D9FFF, #63E2B7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(77, 159, 255, 0.4);
  animation: portalPulse 3s ease-in-out infinite;
}

.portal-icon {
  font-size: 48rpx;
  color: white;
  filter: drop-shadow(0 0 10rpx rgba(255, 255, 255, 0.8));
  animation: portalIconSpin 6s linear infinite;
}

.portal-rings {
  position: absolute;
  width: 100%;
  height: 100%;
}

.ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: portalRing 4s ease-in-out infinite;
}

.ring:nth-child(1) {
  width: 110%;
  height: 110%;
  animation-delay: 0s;
}

.ring:nth-child(2) {
  width: 130%;
  height: 130%;
  animation-delay: 1.3s;
}

.ring:nth-child(3) {
  width: 150%;
  height: 150%;
  animation-delay: 2.6s;
}

/* 扇形菜单 */
.portal-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 20rpx;
}

.menu-item {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  animation: menuItemAppear 0.5s ease-out;
  backdrop-filter: blur(10rpx);
}

.menu-item:active {
  transform: scale(0.9);
}

.menu-item.home {
  bottom: 0;
  right: 140rpx;
  animation-delay: 0.1s;
  border: 3rpx solid #FF9500;
}

.menu-item.achievements {
  bottom: 100rpx;
  right: 100rpx;
  animation-delay: 0.2s;
  border: 3rpx solid #FFD700;
}

.menu-item.settings {
  bottom: 180rpx;
  right: 40rpx;
  animation-delay: 0.3s;
  border: 3rpx solid #4D9FFF;
}

.menu-item.report {
  bottom: 240rpx;
  right: -20rpx;
  animation-delay: 0.4s;
  border: 3rpx solid #63E2B7;
}

.menu-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.menu-text {
  font-size: 16rpx;
  color: #333;
  font-weight: bold;
}

/* 庆祝动画层 */
.celebration-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.celebration-layer.active {
  opacity: 1;
}

.confetti-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.confetti {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background: linear-gradient(45deg, #FF6B9D, #4D9FFF, #63E2B7, #FFD76A);
  animation: confettiFall 3s ease-out infinite;
}

.confetti:nth-child(odd) {
  background: linear-gradient(45deg, #FFD76A, #FF6B9D);
  animation-duration: 2.5s;
}

.confetti:nth-child(3n) {
  background: linear-gradient(45deg, #63E2B7, #4D9FFF);
  animation-duration: 3.5s;
}

/* 彩带随机位置 */
.confetti-0 { left: 5%; animation-delay: 0s; }
.confetti-1 { left: 15%; animation-delay: 0.2s; }
.confetti-2 { left: 25%; animation-delay: 0.4s; }
.confetti-3 { left: 35%; animation-delay: 0.6s; }
.confetti-4 { left: 45%; animation-delay: 0.8s; }
.confetti-5 { left: 55%; animation-delay: 1s; }
.confetti-6 { left: 65%; animation-delay: 1.2s; }
.confetti-7 { left: 75%; animation-delay: 1.4s; }
.confetti-8 { left: 85%; animation-delay: 1.6s; }
.confetti-9 { left: 95%; animation-delay: 1.8s; }
.confetti-10 { left: 10%; animation-delay: 0.1s; }
.confetti-11 { left: 20%; animation-delay: 0.3s; }
.confetti-12 { left: 30%; animation-delay: 0.5s; }
.confetti-13 { left: 40%; animation-delay: 0.7s; }
.confetti-14 { left: 50%; animation-delay: 0.9s; }
.confetti-15 { left: 60%; animation-delay: 1.1s; }
.confetti-16 { left: 70%; animation-delay: 1.3s; }
.confetti-17 { left: 80%; animation-delay: 1.5s; }
.confetti-18 { left: 90%; animation-delay: 1.7s; }
.confetti-19 { left: 12%; animation-delay: 1.9s; }

.celebration-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 30rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20rpx);
  animation: celebrationTextAppear 0.5s ease-out;
}

.celebration-message {
  font-size: 32rpx;
  font-weight: bold;
  background: linear-gradient(45deg, #FF6B9D, #4D9FFF, #63E2B7);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbowText 2s ease-in-out infinite;
}

/* 卡片角色区域 */
.card-character {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.character-emoji {
  font-size: 48rpx;
  animation: characterBounce 2s ease-in-out infinite;
}

.character-face {
  font-size: 32rpx;
  animation: faceWink 3s ease-in-out infinite;
}

/* 卡片内容区域 */
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 8rpx;
}

.card-subtitle {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

/* 任务预览区域 */
.task-preview {
  flex: 1;
  margin-bottom: 15rpx;
}

.mini-task {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
  padding: 8rpx 12rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 15rpx;
  transition: all 0.2s ease;
}

.task-status-icon {
  font-size: 20rpx;
  flex-shrink: 0;
  animation: statusPulse 2s ease-in-out infinite;
}

.mini-task-name {
  font-size: 20rpx;
  color: #555;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

/* 卡片进度区域 */
.card-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  margin-top: auto;
}

.progress-emoji {
  font-size: 24rpx;
  animation: progressEmojiBounce 1.5s ease-in-out infinite;
}

.progress-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
}

/* 可爱的底部按钮 */
.cute-bottom-controls {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 25rpx 40rpx 45rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8), transparent);
  backdrop-filter: blur(20rpx);
  z-index: 20;
}

.cute-button {
  position: relative;
  width: 160rpx;
  height: 120rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 3rpx solid transparent;
}

.cute-button:active {
  transform: scale(0.95) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.cute-button.bouncing {
  animation: cuteButtonBounce 0.5s ease-in-out;
}

.back-button {
  border-color: #FF9500;
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 255, 255, 0.9));
}

.achievement-button {
  border-color: #FFD700;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 255, 255, 0.9));
}

.button-decoration {
  position: absolute;
  top: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10rpx;
}

.deco-item {
  font-size: 20rpx;
  animation: decoFloat 2s ease-in-out infinite;
}

.deco-item:nth-child(2) {
  animation-delay: 0.5s;
}

.deco-item:nth-child(3) {
  animation-delay: 1s;
}

.button-content {
  text-align: center;
  margin-top: 10rpx;
}

.button-icon {
  display: block;
  font-size: 36rpx;
  margin-bottom: 8rpx;
  animation: iconPulse 2s ease-in-out infinite;
}

.button-text {
  font-size: 20rpx;
  color: #333;
  font-weight: 600;
}

/* 删除复杂的火箭引擎动画，保持简洁 */



/* 沉浸式星球探险动画关键帧 */

/* 宇宙背景动画 */
@keyframes starTwinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes meteorFall {
  0% { transform: translateY(-100vh) translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh) translateX(-200rpx); opacity: 0; }
}

@keyframes nebulaFloat {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.3; }
  50% { transform: translateY(-20rpx) scale(1.1); opacity: 0.5; }
}

/* 探险家角色动画 */
@keyframes characterHappy {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5rpx) scale(1.05); }
}

@keyframes characterExcited {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-8rpx) rotate(-2deg); }
  75% { transform: translateY(-8rpx) rotate(2deg); }
}

@keyframes accessoryFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-3rpx) rotate(5deg); }
}

/* 能量光环动画 */
@keyframes auraRing {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.8; }
  25% { transform: translateY(-10rpx) rotate(90deg); opacity: 1; }
  50% { transform: translateY(-5rpx) rotate(180deg); opacity: 0.6; }
  75% { transform: translateY(-8rpx) rotate(270deg); opacity: 1; }
}

/* 星球系统动画 */
@keyframes orbitRotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes planetGlow {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

@keyframes atmosphereFloat {
  0%, 100% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0.7; }
  33% { transform: translateY(-5rpx) translateX(3rpx) rotate(120deg); opacity: 0.9; }
  66% { transform: translateY(2rpx) translateX(-3rpx) rotate(240deg); opacity: 0.5; }
}

@keyframes beamPulse {
  0%, 100% { opacity: 0.6; transform: scaleY(1); }
  50% { opacity: 1; transform: scaleY(1.2); }
}

@keyframes beamParticle {
  0% { transform: translateY(200rpx); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(0); opacity: 0; }
}

/* 魔法传送门动画 */
@keyframes portalPulse {
  0%, 100% { transform: scale(1); box-shadow: 0 8rpx 32rpx rgba(77, 159, 255, 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 12rpx 48rpx rgba(77, 159, 255, 0.6); }
}

@keyframes portalIconSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes portalRing {
  0% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

@keyframes menuItemAppear {
  0% { transform: scale(0) rotate(-180deg); opacity: 0; }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

/* 庆祝动画 */
@keyframes confettiFall {
  0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
  100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

@keyframes celebrationTextAppear {
  0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes rainbowText {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}


