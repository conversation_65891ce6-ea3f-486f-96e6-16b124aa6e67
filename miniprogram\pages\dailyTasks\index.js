// 《能量星球》舰长日志 - 今日任务逻辑
const { dailyTasksSystem } = require('../../utils/dailyTasksSystem');
const { habitTracker } = require('../../utils/habitTracker');
const { realWorldTasks } = require('../../utils/realWorldTasks');

Page({
  data: {
    // 今日进度概览
    completedTasks: 0,
    totalTasks: 12,
    dailyProgress: 0,

    // 能量系统
    todayLoveEnergy: 0,
    todayWisdomEnergy: 0,

    // 任务统计
    lifeTasksStats: { completed: 0, total: 3 },
    familyTasksStats: { completed: 0, total: 3 },
    socialTasksStats: { completed: 0, total: 3 },
    participationTasksStats: { completed: 0, total: 3 },

    // 任务进度百分比
    lifeTasksProgress: 0,
    familyTasksProgress: 0,
    socialTasksProgress: 0,
    participationTasksProgress: 0,

    // 任务详情弹窗
    selectedCategory: null,
    selectedCategoryInfo: {},
    selectedCategoryTasks: [],

    // 今日成就
    todayAchievements: [],

    // 习惯追踪
    longestStreak: 0,
    habitChart: [60, 80, 45, 90, 70, 85, 95],

    // 魔法传送门
    portalExpanded: false,

    // 庆祝动画
    celebrating: false,
    celebrationMessage: '',

    // 页面状态
    loading: false,
    refreshing: false
  },

  onLoad: function (options) {
    console.log('今日任务中心加载');
    this.initializeTaskCenter();
  },

  onReady: function () {
    console.log('今日任务中心渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新数据
    this.refreshAllData();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshAllData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化任务中心
  initializeTaskCenter() {
    this.setData({ loading: true });

    // 生成今日任务
    this.generateDailyTasks();

    // 加载任务数据
    this.loadTasksData();

    // 计算今日进度
    this.calculateDailyProgress();

    // 加载能量数据
    this.loadEnergyData();

    // 加载成就数据
    this.loadAchievements();

    // 加载习惯数据
    this.loadHabits();

    this.setData({ loading: false });
  },

  // 生成今日任务
  generateDailyTasks() {
    // 简化版本，直接创建示例数据
    const todayTasks = {
      lifeTasks: [
        { id: 'life_1', name: '早起刷牙', status: 'completed', energyReward: { type: 'wisdom', amount: 5 } },
        { id: 'life_2', name: '整理房间', status: 'in_progress', energyReward: { type: 'wisdom', amount: 8 } },
        { id: 'life_3', name: '按时睡觉', status: 'pending', energyReward: { type: 'love', amount: 6 } }
      ],
      familyTasks: [
        { id: 'family_1', name: '帮助妈妈', status: 'completed', energyReward: { type: 'love', amount: 10 } },
        { id: 'family_2', name: '陪伴家人', status: 'pending', energyReward: { type: 'love', amount: 8 } },
        { id: 'family_3', name: '表达感谢', status: 'pending', energyReward: { type: 'love', amount: 6 } }
      ],
      socialTasks: [
        { id: 'social_1', name: '礼貌问候', status: 'completed', energyReward: { type: 'love', amount: 5 } },
        { id: 'social_2', name: '分享玩具', status: 'pending', energyReward: { type: 'love', amount: 8 } },
        { id: 'social_3', name: '帮助同学', status: 'pending', energyReward: { type: 'love', amount: 10 } }
      ],
      participationTasks: [
        { id: 'participation_1', name: '垃圾分类', status: 'pending', energyReward: { type: 'wisdom', amount: 8 } },
        { id: 'participation_2', name: '节约用水', status: 'pending', energyReward: { type: 'wisdom', amount: 6 } },
        { id: 'participation_3', name: '爱护环境', status: 'pending', energyReward: { type: 'love', amount: 10 } }
      ]
    };

    // 保存到本地存储
    const today = new Date().toDateString();
    wx.setStorageSync(`dailyTasks_${today}`, todayTasks);
  },

  // 加载任务数据
  loadTasksData() {
    const today = new Date().toDateString();
    const todayTasks = wx.getStorageSync(`dailyTasks_${today}`) || {
      lifeTasks: [],
      familyTasks: [],
      socialTasks: [],
      participationTasks: []
    };

    // 处理各类别任务预览数据（只显示前3个）
    this.setData({
      lifeTasksPreview: todayTasks.lifeTasks.slice(0, 3),
      familyTasksPreview: todayTasks.familyTasks.slice(0, 3),
      socialTasksPreview: todayTasks.socialTasks.slice(0, 3),
      participationTasksPreview: todayTasks.participationTasks.slice(0, 3),

      // 计算各类别统计
      lifeTasksStats: this.calculateCategoryStats(todayTasks.lifeTasks),
      familyTasksStats: this.calculateCategoryStats(todayTasks.familyTasks),
      socialTasksStats: this.calculateCategoryStats(todayTasks.socialTasks),
      participationTasksStats: this.calculateCategoryStats(todayTasks.participationTasks)
    });
  },

  // 计算类别统计
  calculateCategoryStats(tasks) {
    const total = tasks.length;
    const completed = tasks.filter(task => task.status === 'completed').length;
    return { completed, total };
  },

  // 计算今日进度
  calculateDailyProgress() {
    const stats = this.data;
    const totalTasks = stats.lifeTasksStats.total + stats.familyTasksStats.total +
                      stats.socialTasksStats.total + stats.participationTasksStats.total;
    const completedTasks = stats.lifeTasksStats.completed + stats.familyTasksStats.completed +
                          stats.socialTasksStats.completed + stats.participationTasksStats.completed;

    const dailyProgress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // 计算各类别进度
    const lifeTasksProgress = stats.lifeTasksStats.total > 0 ?
      Math.round((stats.lifeTasksStats.completed / stats.lifeTasksStats.total) * 100) : 0;
    const familyTasksProgress = stats.familyTasksStats.total > 0 ?
      Math.round((stats.familyTasksStats.completed / stats.familyTasksStats.total) * 100) : 0;
    const socialTasksProgress = stats.socialTasksStats.total > 0 ?
      Math.round((stats.socialTasksStats.completed / stats.socialTasksStats.total) * 100) : 0;
    const participationTasksProgress = stats.participationTasksStats.total > 0 ?
      Math.round((stats.participationTasksStats.completed / stats.participationTasksStats.total) * 100) : 0;

    this.setData({
      completedTasks,
      totalTasks,
      dailyProgress,
      lifeTasksProgress,
      familyTasksProgress,
      socialTasksProgress,
      participationTasksProgress
    });
  },

  // 加载成就数据
  loadAchievements() {
    const todayAchievements = [
      { id: 'early_bird', badge: '🌅', name: '早起鸟儿' },
      { id: 'helper', badge: '🤝', name: '小帮手' }
    ];

    this.setData({ todayAchievements });
  },

  // 加载习惯数据
  loadHabits() {
    const longestStreak = 7; // 示例数据
    this.setData({ longestStreak });
  },

  // 加载能量数据
  loadEnergyData() {
    const today = new Date().toDateString();
    const todayEnergyData = wx.getStorageSync(`energy_${today}`) || {
      loveEnergy: 0,
      wisdomEnergy: 0
    };

    // 计算能量强度（用于光环效果）
    const maxEnergy = 50; // 假设每日最大能量
    const loveIntensity = Math.min(todayEnergyData.loveEnergy / maxEnergy, 1);
    const wisdomIntensity = Math.min(todayEnergyData.wisdomEnergy / maxEnergy, 1);

    this.setData({
      todayLoveEnergy: todayEnergyData.loveEnergy,
      todayWisdomEnergy: todayEnergyData.wisdomEnergy,
      loveEnergyIntensity: loveIntensity,
      wisdomEnergyIntensity: wisdomIntensity
    });
  },

  // 刷新所有数据
  refreshAllData() {
    this.setData({ refreshing: true });

    this.loadTasksData();
    this.calculateDailyProgress();
    this.loadEnergyData();
    this.loadAchievements();
    this.loadHabits();

    setTimeout(() => {
      this.setData({ refreshing: false });
    }, 1000);
  },

  // 打开任务类别
  onOpenTaskCategory(e) {
    const category = e.currentTarget.dataset.category;
    console.log('打开任务类别:', category);

    const categoryInfo = {
      life: {
        title: '🏠 生活习惯任务',
        subtitle: '培养良好的生活习惯，让每一天都井井有条！'
      },
      family: {
        title: '❤️ 家庭关爱任务',
        subtitle: '用爱心温暖家庭，让关爱传递到每个角落！'
      },
      social: {
        title: '🤝 社交友谊任务',
        subtitle: '结交更多好朋友，让友谊之花绽放！'
      },
      participation: {
        title: '🌍 社会参与任务',
        subtitle: '保护我们美丽的地球，做一个负责任的小公民！'
      }
    };

    // 获取对应类别的任务数据
    const taskKey = `${category}TasksPreview`;
    const tasks = this.data[taskKey] || [];

    this.setData({
      selectedCategory: category,
      selectedCategoryInfo: categoryInfo[category],
      selectedCategoryTasks: tasks
    });
  },

  // 切换任务状态
  onToggleTask(e) {
    const taskId = e.currentTarget.dataset.taskId;
    console.log('切换任务状态:', taskId);

    // 这里可以添加任务状态切换逻辑
    this.triggerCelebration('任务状态已更新！✨');
  },

  // 关闭任务详情弹窗
  onCloseModal() {
    this.setData({
      selectedCategory: null,
      selectedCategoryInfo: {},
      selectedCategoryTasks: []
    });
  },

  // 弹窗内容点击（阻止冒泡）
  onModalContentTap() {
    // 阻止事件冒泡，避免关闭弹窗
  },

  // 点击探险家角色
  onExplorerTap() {
    console.log('点击探险家角色');

    // 触发庆祝动画
    this.triggerCelebration('你好！我是小探险家！✨');

    // 临时改变表情
    const originalFace = this.data.explorerFace;
    this.setData({
      explorerFace: '😄',
      explorerMood: 'excited'
    });

    setTimeout(() => {
      this.setData({
        explorerFace: originalFace,
        explorerMood: 'happy'
      });
    }, 2000);
  },

  // 触发庆祝动画
  triggerCelebration(message) {
    this.setData({
      celebrating: true,
      celebrationMessage: message
    });

    setTimeout(() => {
      this.setData({
        celebrating: false,
        celebrationMessage: ''
      });
    }, 3000);
  },



  // 打开成就页面
  onOpenAchievements() {
    console.log('打开成就页面');
    wx.showToast({
      title: '成就系统开发中...',
      icon: 'none'
    });
  },

  // 打开习惯追踪
  onOpenHabits() {
    console.log('打开习惯追踪');
    wx.showToast({
      title: '习惯追踪开发中...',
      icon: 'none'
    });
  },

  // 切换魔法传送门
  onTogglePortal() {
    this.setData({
      portalExpanded: !this.data.portalExpanded
    });
  },

  // 返回星球基地
  onGoBack() {
    console.log('返回星球基地');
    this.setData({ portalExpanded: false });

    // 触发传送动画
    this.triggerCelebration('正在传送回星球基地...🚀');

    setTimeout(() => {
      wx.navigateBack({
        delta: 1
      });
    }, 1000);
  },

  // 打开设置
  onOpenSettings() {
    console.log('打开设置');
    this.setData({ portalExpanded: false });

    wx.showModal({
      title: '⚙️ 设置中心',
      content: '设置功能正在开发中...\n\n将提供：\n• 个性化设置\n• 提醒设置\n• 主题切换\n• 隐私设置',
      showCancel: false,
      confirmText: '了解了',
      confirmColor: '#4D9FFF'
    });
  }
});
