// 《能量星球》舰长日志 - 今日任务逻辑
const { dailyTasksSystem } = require('../../utils/dailyTasksSystem');
const { habitTracker } = require('../../utils/habitTracker');
const { realWorldTasks } = require('../../utils/realWorldTasks');

Page({
  data: {
    // 探险家角色状态
    explorerFace: '😊',
    explorerMood: 'happy',
    explorerLevel: 1,

    // 能量系统
    todayLoveEnergy: 0,
    todayWisdomEnergy: 0,
    loveEnergyIntensity: 0.5,
    wisdomEnergyIntensity: 0.5,

    // 星球系统状态
    systemRotation: 0,
    orbitRadius1: 300,
    orbitRadius2: 400,

    // 星球位置和状态
    lifePlanetAngle: 0,
    lovePlanetAngle: 90,
    friendshipPlanetAngle: 180,
    earthPlanetAngle: 270,

    lifePlanetActive: false,
    lovePlanetActive: false,
    friendshipPlanetActive: false,
    earthPlanetActive: false,

    // 任务统计
    lifeTasksStats: { completed: 0, total: 3 },
    familyTasksStats: { completed: 0, total: 3 },
    socialTasksStats: { completed: 0, total: 3 },
    participationTasksStats: { completed: 0, total: 3 },

    // 星球详情弹窗
    selectedPlanet: null,
    selectedPlanetInfo: {},
    selectedPlanetTasks: [],

    // 魔法传送门
    portalExpanded: false,

    // 庆祝动画
    celebrating: false,
    celebrationMessage: '',

    // 页面状态
    loading: false,
    refreshing: false
  },

  onLoad: function (options) {
    console.log('星球探险系统加载');
    this.initializeSpaceAdventure();
  },

  onReady: function () {
    console.log('星球探险系统渲染完成');
    this.startPlanetaryMotion();
  },

  onShow: function () {
    // 每次显示时刷新数据
    this.refreshAllData();
    this.updateExplorerMood();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshAllData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化星球探险系统
  initializeSpaceAdventure() {
    this.setData({ loading: true });

    // 生成今日任务
    this.generateDailyTasks();

    // 加载任务数据
    this.loadTasksData();

    // 计算探险家等级和状态
    this.calculateExplorerStatus();

    // 加载能量数据
    this.loadEnergyData();

    // 初始化星球状态
    this.initializePlanets();

    this.setData({ loading: false });
  },

  // 启动星球运动
  startPlanetaryMotion() {
    // 启动星球轨道运动
    this.planetMotionTimer = setInterval(() => {
      this.setData({
        lifePlanetAngle: (this.data.lifePlanetAngle + 0.5) % 360,
        lovePlanetAngle: (this.data.lovePlanetAngle + 0.5) % 360,
        friendshipPlanetAngle: (this.data.friendshipPlanetAngle + 0.3) % 360,
        earthPlanetAngle: (this.data.earthPlanetAngle + 0.3) % 360
      });
    }, 100);
  },

  // 初始化星球状态
  initializePlanets() {
    // 根据任务完成情况设置星球状态
    const stats = this.data;

    this.setData({
      lifePlanetActive: stats.lifeTasksStats.completed > 0,
      lovePlanetActive: stats.familyTasksStats.completed > 0,
      friendshipPlanetActive: stats.socialTasksStats.completed > 0,
      earthPlanetActive: stats.participationTasksStats.completed > 0
    });
  },

  // 生成今日任务
  generateDailyTasks() {
    // 获取用户数据用于个性化推荐
    const userData = wx.getStorageSync('userData') || {};

    // 生成四大类别的今日任务
    const todayTasks = dailyTasksSystem.generateDailyTasks(userData);

    // 保存到本地存储
    const today = new Date().toDateString();
    wx.setStorageSync(`dailyTasks_${today}`, todayTasks);
  },

  // 加载任务数据
  loadTasksData() {
    const today = new Date().toDateString();
    const todayTasks = wx.getStorageSync(`dailyTasks_${today}`) || {
      lifeTasks: [],
      familyTasks: [],
      socialTasks: [],
      participationTasks: []
    };

    // 处理各类别任务预览数据（只显示前3个）
    this.setData({
      lifeTasksPreview: todayTasks.lifeTasks.slice(0, 3),
      familyTasksPreview: todayTasks.familyTasks.slice(0, 3),
      socialTasksPreview: todayTasks.socialTasks.slice(0, 3),
      participationTasksPreview: todayTasks.participationTasks.slice(0, 3),

      // 计算各类别统计
      lifeTasksStats: this.calculateCategoryStats(todayTasks.lifeTasks),
      familyTasksStats: this.calculateCategoryStats(todayTasks.familyTasks),
      socialTasksStats: this.calculateCategoryStats(todayTasks.socialTasks),
      participationTasksStats: this.calculateCategoryStats(todayTasks.participationTasks)
    });
  },

  // 计算类别统计
  calculateCategoryStats(tasks) {
    const total = tasks.length;
    const completed = tasks.filter(task => task.status === 'completed').length;
    return { completed, total };
  },

  // 计算探险家状态
  calculateExplorerStatus() {
    const stats = this.data;
    const totalTasks = stats.lifeTasksStats.total + stats.familyTasksStats.total +
                      stats.socialTasksStats.total + stats.participationTasksStats.total;
    const completedTasks = stats.lifeTasksStats.completed + stats.familyTasksStats.completed +
                          stats.socialTasksStats.completed + stats.participationTasksStats.completed;

    const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // 计算探险家等级
    let explorerLevel = 1;
    if (completedTasks >= 10) explorerLevel = 4;
    else if (completedTasks >= 6) explorerLevel = 3;
    else if (completedTasks >= 3) explorerLevel = 2;

    // 设置探险家表情和心情
    let explorerFace = '😊';
    let explorerMood = 'happy';

    if (progress === 100) {
      explorerFace = '🤩';
      explorerMood = 'excited';
    } else if (progress >= 75) {
      explorerFace = '😄';
      explorerMood = 'excited';
    } else if (progress >= 50) {
      explorerFace = '😊';
      explorerMood = 'happy';
    } else if (completedTasks > 0) {
      explorerFace = '🙂';
      explorerMood = 'happy';
    } else {
      explorerFace = '😌';
      explorerMood = 'calm';
    }

    this.setData({
      explorerLevel,
      explorerFace,
      explorerMood
    });
  },

  // 加载能量数据
  loadEnergyData() {
    const today = new Date().toDateString();
    const todayEnergyData = wx.getStorageSync(`energy_${today}`) || {
      loveEnergy: 0,
      wisdomEnergy: 0
    };

    // 计算能量强度（用于光环效果）
    const maxEnergy = 50; // 假设每日最大能量
    const loveIntensity = Math.min(todayEnergyData.loveEnergy / maxEnergy, 1);
    const wisdomIntensity = Math.min(todayEnergyData.wisdomEnergy / maxEnergy, 1);

    this.setData({
      todayLoveEnergy: todayEnergyData.loveEnergy,
      todayWisdomEnergy: todayEnergyData.wisdomEnergy,
      loveEnergyIntensity: loveIntensity,
      wisdomEnergyIntensity: wisdomIntensity
    });
  },

  // 更新探险家心情
  updateExplorerMood() {
    this.calculateExplorerStatus();
  },

  // 刷新所有数据
  refreshAllData() {
    this.setData({ refreshing: true });

    this.loadTasksData();
    this.calculateExplorerStatus();
    this.loadEnergyData();
    this.initializePlanets();

    setTimeout(() => {
      this.setData({ refreshing: false });
    }, 1000);
  },

  // 点击星球
  onPlanetTap(e) {
    const planet = e.currentTarget.dataset.planet;
    console.log('点击星球:', planet);

    // 设置星球为激活状态
    this.setData({
      [`${planet}PlanetActive`]: true
    });

    // 显示星球详情
    this.showPlanetDetail(planet);

    // 0.5秒后取消激活状态
    setTimeout(() => {
      this.setData({
        [`${planet}PlanetActive`]: false
      });
    }, 500);
  },

  // 显示星球详情
  showPlanetDetail(planet) {
    const planetInfo = {
      life: {
        title: '🏠 生活星球',
        subtitle: '培养良好的生活习惯，让每一天都井井有条！',
        tasks: this.data.lifeTasksPreview || []
      },
      love: {
        title: '❤️ 爱心星球',
        subtitle: '用爱心温暖家庭，让关爱传递到每个角落！',
        tasks: this.data.familyTasksPreview || []
      },
      friendship: {
        title: '🤝 友谊星球',
        subtitle: '结交更多好朋友，让友谊之花绽放！',
        tasks: this.data.socialTasksPreview || []
      },
      earth: {
        title: '🌍 地球星球',
        subtitle: '保护我们美丽的地球，做一个负责任的小公民！',
        tasks: this.data.participationTasksPreview || []
      }
    };

    this.setData({
      selectedPlanet: planet,
      selectedPlanetInfo: planetInfo[planet],
      selectedPlanetTasks: planetInfo[planet].tasks
    });
  },

  // 关闭星球详情弹窗
  onCloseModal() {
    this.setData({
      selectedPlanet: null,
      selectedPlanetInfo: {},
      selectedPlanetTasks: []
    });
  },

  // 弹窗内容点击（阻止冒泡）
  onModalContentTap() {
    // 阻止事件冒泡，避免关闭弹窗
  },

  // 点击探险家角色
  onExplorerTap() {
    console.log('点击探险家角色');

    // 触发庆祝动画
    this.triggerCelebration('你好！我是小探险家！✨');

    // 临时改变表情
    const originalFace = this.data.explorerFace;
    this.setData({
      explorerFace: '😄',
      explorerMood: 'excited'
    });

    setTimeout(() => {
      this.setData({
        explorerFace: originalFace,
        explorerMood: 'happy'
      });
    }, 2000);
  },

  // 触发庆祝动画
  triggerCelebration(message) {
    this.setData({
      celebrating: true,
      celebrationMessage: message
    });

    setTimeout(() => {
      this.setData({
        celebrating: false,
        celebrationMessage: ''
      });
    }, 3000);
  },

  // 切换魔法传送门
  onTogglePortal() {
    this.setData({
      portalExpanded: !this.data.portalExpanded
    });
  },

  // 打开成就收藏馆
  onOpenAchievements() {
    console.log('打开成就收藏馆');
    this.setData({ portalExpanded: false });

    this.triggerCelebration('成就收藏馆即将开放！🏅');

    setTimeout(() => {
      wx.showModal({
        title: '🏅 成就收藏馆',
        content: '哇！成就收藏馆正在建设中呢！\n\n很快就能收集到：\n🌟 生活习惯大师\n💖 爱心传递者\n🤝 友谊建造师\n🌍 地球守护者\n\n敬请期待哦！',
        showCancel: false,
        confirmText: '好期待！',
        confirmColor: '#FFD700'
      });
    }, 1000);
  },

  // 打开设置中心
  onOpenSettings() {
    console.log('打开设置中心');
    this.setData({ portalExpanded: false });

    wx.showModal({
      title: '⚙️ 设置中心',
      content: '设置功能正在开发中...\n\n将提供：\n• 个性化设置\n• 提醒设置\n• 主题切换\n• 隐私设置',
      showCancel: false,
      confirmText: '了解了',
      confirmColor: '#4D9FFF'
    });
  },

  // 打开成长报告
  onOpenReport() {
    console.log('打开成长报告');
    this.setData({ portalExpanded: false });

    wx.showModal({
      title: '📊 成长报告',
      content: '成长报告功能正在开发中...\n\n将提供：\n• 每日成长数据\n• 习惯养成分析\n• 能力发展图表\n• 个性化建议',
      showCancel: false,
      confirmText: '期待中',
      confirmColor: '#63E2B7'
    });
  },

  // 返回星球基地
  onGoBack() {
    console.log('返回星球基地');
    this.setData({ portalExpanded: false });

    // 清理定时器
    if (this.planetMotionTimer) {
      clearInterval(this.planetMotionTimer);
    }

    // 触发传送动画
    this.triggerCelebration('正在传送回星球基地...🚀');

    setTimeout(() => {
      wx.navigateBack({
        delta: 1
      });
    }, 1000);
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.planetMotionTimer) {
      clearInterval(this.planetMotionTimer);
    }
  }
});
