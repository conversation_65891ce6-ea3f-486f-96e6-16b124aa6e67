// 《能量星球》舰长日志 - 今日任务逻辑
const { dailyTasksSystem } = require('../../utils/dailyTasksSystem');
const { habitTracker } = require('../../utils/habitTracker');
const { realWorldTasks } = require('../../utils/realWorldTasks');

Page({
  data: {
    // 今日进度数据
    dailyProgress: 0,
    completedTasks: 0,
    totalTasks: 0,
    progressMessage: '今天又是充满惊喜的一天！',

    // 今日能量获得
    todayLoveEnergy: 0,
    todayWisdomEnergy: 0,
    
    // 四大任务类别预览数据
    lifeTasksPreview: [],
    familyTasksPreview: [],
    socialTasksPreview: [],
    participationTasksPreview: [],
    
    // 各类别统计
    lifeTasksStats: { completed: 0, total: 0 },
    familyTasksStats: { completed: 0, total: 0 },
    socialTasksStats: { completed: 0, total: 0 },
    participationTasksStats: { completed: 0, total: 0 },
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 动画状态
    backAnimating: false,
    achievementsAnimating: false
  },

  onLoad: function (options) {
    console.log('舰长日志加载');
    this.initializeDailyTasks();
  },

  onReady: function () {
    console.log('舰长日志渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新数据
    this.refreshAllData();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshAllData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化今日任务系统
  initializeDailyTasks() {
    this.setData({ loading: true });
    
    // 生成今日任务
    this.generateDailyTasks();
    
    // 加载任务数据
    this.loadTasksData();
    
    // 计算今日进度
    this.calculateDailyProgress();
    
    // 加载能量数据
    this.loadEnergyData();
    
    this.setData({ loading: false });
  },

  // 生成今日任务
  generateDailyTasks() {
    // 获取用户数据用于个性化推荐
    const userData = wx.getStorageSync('userData') || {};
    
    // 生成四大类别的今日任务
    const todayTasks = dailyTasksSystem.generateDailyTasks(userData);
    
    // 保存到本地存储
    const today = new Date().toDateString();
    wx.setStorageSync(`dailyTasks_${today}`, todayTasks);
  },

  // 加载任务数据
  loadTasksData() {
    const today = new Date().toDateString();
    const todayTasks = wx.getStorageSync(`dailyTasks_${today}`) || {
      lifeTasks: [],
      familyTasks: [],
      socialTasks: [],
      participationTasks: []
    };

    // 处理各类别任务预览数据（只显示前3个）
    this.setData({
      lifeTasksPreview: todayTasks.lifeTasks.slice(0, 3),
      familyTasksPreview: todayTasks.familyTasks.slice(0, 3),
      socialTasksPreview: todayTasks.socialTasks.slice(0, 3),
      participationTasksPreview: todayTasks.participationTasks.slice(0, 3),
      
      // 计算各类别统计
      lifeTasksStats: this.calculateCategoryStats(todayTasks.lifeTasks),
      familyTasksStats: this.calculateCategoryStats(todayTasks.familyTasks),
      socialTasksStats: this.calculateCategoryStats(todayTasks.socialTasks),
      participationTasksStats: this.calculateCategoryStats(todayTasks.participationTasks)
    });
  },

  // 计算类别统计
  calculateCategoryStats(tasks) {
    const total = tasks.length;
    const completed = tasks.filter(task => task.status === 'completed').length;
    return { completed, total };
  },

  // 计算今日进度
  calculateDailyProgress() {
    const stats = this.data;
    const totalTasks = stats.lifeTasksStats.total + stats.familyTasksStats.total +
                      stats.socialTasksStats.total + stats.participationTasksStats.total;
    const completedTasks = stats.lifeTasksStats.completed + stats.familyTasksStats.completed +
                          stats.socialTasksStats.completed + stats.participationTasksStats.completed;

    const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // 生成鼓励性的进度消息
    let progressMessage = '';
    if (progress === 100) {
      progressMessage = '哇！今天的任务全部完成了，你真是太棒了！🎉';
    } else if (progress >= 75) {
      progressMessage = '太厉害了！马上就要全部完成啦！💪';
    } else if (progress >= 50) {
      progressMessage = '很棒哦！已经完成一半以上了！😊';
    } else if (progress >= 25) {
      progressMessage = '加油！你已经开始行动了！🌟';
    } else if (completedTasks > 0) {
      progressMessage = '很好！迈出了第一步！✨';
    } else {
      progressMessage = '今天又是充满惊喜的一天！准备开始冒险吧！🚀';
    }

    this.setData({
      totalTasks,
      completedTasks,
      dailyProgress: progress,
      progressMessage
    });
  },

  // 加载能量数据
  loadEnergyData() {
    const today = new Date().toDateString();
    const todayEnergyData = wx.getStorageSync(`energy_${today}`) || {
      loveEnergy: 0,
      wisdomEnergy: 0
    };
    
    this.setData({
      todayLoveEnergy: todayEnergyData.loveEnergy,
      todayWisdomEnergy: todayEnergyData.wisdomEnergy
    });
  },

  // 刷新所有数据
  refreshAllData() {
    this.setData({ refreshing: true });

    this.loadTasksData();
    this.calculateDailyProgress();
    this.loadEnergyData();

    setTimeout(() => {
      this.setData({ refreshing: false });
    }, 1000);
  },

  // 打开生活小管家任务
  onOpenLifeTasks() {
    console.log('打开生活小管家任务');
    wx.showModal({
      title: '🏠 生活小管家',
      content: '哇！生活小管家的任务正在准备中呢！\n\n很快就能帮助你：\n• 养成好的卫生习惯\n• 学会整理房间\n• 管理好时间\n\n敬请期待哦！',
      showCancel: false,
      confirmText: '好期待！',
      confirmColor: '#FF9500'
    });
  },

  // 打开爱心小天使任务
  onOpenFamilyTasks() {
    console.log('打开爱心小天使任务');
    wx.showModal({
      title: '❤️ 爱心小天使',
      content: '爱心小天使的任务正在准备中呢！\n\n很快就能帮助你：\n• 帮助家人做家务\n• 表达对家人的爱\n• 照顾弟弟妹妹\n\n敬请期待哦！',
      showCancel: false,
      confirmText: '好期待！',
      confirmColor: '#FF6B9D'
    });
  },

  // 打开友谊小使者任务
  onOpenSocialTasks() {
    console.log('打开友谊小使者任务');
    wx.showModal({
      title: '🤝 友谊小使者',
      content: '友谊小使者的任务正在准备中呢！\n\n很快就能帮助你：\n• 结交更多好朋友\n• 学会礼貌用语\n• 参与团队活动\n\n敬请期待哦！',
      showCancel: false,
      confirmText: '好期待！',
      confirmColor: '#63E2B7'
    });
  },

  // 打开地球小卫士任务
  onOpenSocialParticipationTasks() {
    console.log('打开地球小卫士任务');
    wx.showModal({
      title: '🌍 地球小卫士',
      content: '地球小卫士的任务正在准备中呢！\n\n很快就能帮助你：\n• 保护美丽的地球\n• 学会垃圾分类\n• 了解传统文化\n\n敬请期待哦！',
      showCancel: false,
      confirmText: '好期待！',
      confirmColor: '#4CAF50'
    });
  },

  // 打开我的奖章
  onOpenAchievements() {
    console.log('打开我的奖章');

    // 启动可爱的弹跳动画
    this.setData({ achievementsAnimating: true });

    setTimeout(() => {
      this.setData({ achievementsAnimating: false });

      wx.showModal({
        title: '🏅 我的奖章收藏',
        content: '哇！奖章收藏功能正在准备中呢！\n\n很快就能收集到：\n🌟 生活习惯小达人\n💖 爱心小天使\n🤝 友谊小使者\n🌍 地球小卫士\n\n敬请期待哦！',
        showCancel: false,
        confirmText: '好期待！',
        confirmColor: '#FFD700'
      });
    }, 500);
  },

  // 返回星球
  onGoBack() {
    // 启动可爱的弹跳动画
    this.setData({ backAnimating: true });

    // 播放动画效果
    setTimeout(() => {
      // 动画完成后导航
      wx.navigateBack({
        delta: 1
      });
    }, 500); // 0.5秒动画时长
  }
});
